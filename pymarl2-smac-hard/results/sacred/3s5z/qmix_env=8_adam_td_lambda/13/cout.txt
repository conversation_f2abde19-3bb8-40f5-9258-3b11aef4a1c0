[INFO 15:37:40] pymarl Running command 'my_main'
[INFO 15:37:40] pymarl Started run with ID "13"
[DEBUG 15:37:40] pymarl Starting Heartbeat
[DEBUG 15:37:40] my_main Started
[WARNING 15:37:40] my_main CUDA flag use_cuda was switched OFF automatically because no CUDA devices are available!
[INFO 15:37:40] my_main Experiment Parameters:
[INFO 15:37:40] my_main 

{   'action_selector': 'epsilon_greedy',
    'agent': 'n_rnn',
    'agent_output_type': 'q',
    'batch_size': 128,
    'batch_size_run': 6,
    'buffer_cpu_only': True,
    'buffer_size': 5000,
    'checkpoint_path': '',
    'critic_lr': 0.0005,
    'env': 'sc2',
    'env_args': {   'continuing_episode': False,
                    'debug': False,
                    'difficulty': '7',
                    'game_version': None,
                    'heuristic_ai': False,
                    'heuristic_rest': False,
                    'map_name': '3s5z',
                    'move_amount': 2,
                    'obs_all_health': True,
                    'obs_instead_of_state': False,
                    'obs_last_action': False,
                    'obs_own_health': True,
                    'obs_pathing_grid': False,
                    'obs_terrain_height': False,
                    'obs_timestep_number': False,
                    'replay_dir': '',
                    'replay_prefix': '',
                    'reward_death_value': 10,
                    'reward_defeat': 0,
                    'reward_negative_scale': 0.5,
                    'reward_only_positive': True,
                    'reward_scale': True,
                    'reward_scale_rate': 20,
                    'reward_sparse': False,
                    'reward_win': 200,
                    'seed': 1,
                    'state_last_action': True,
                    'state_timestep_number': False,
                    'step_mul': 8,
                    'window_size_x': 640,
                    'window_size_y': 480},
    'epsilon_anneal_time': 100000,
    'epsilon_finish': 0.05,
    'epsilon_start': 1.0,
    'evaluate': False,
    'gain': 0.01,
    'gamma': 0.99,
    'grad_norm_clip': 10,
    'hypernet_embed': 64,
    'label': 'default_label',
    'learner': 'nq_learner',
    'learner_log_interval': 80000,
    'load_step': 0,
    'local_results_path': 'results',
    'log_interval': 500,
    'lr': 0.001,
    'mac': 'n_mac',
    'mixer': 'qmix',
    'mixing_embed_dim': 32,
    'name': 'qmix_env=8_adam_td_lambda',
    'obs_agent_id': True,
    'obs_last_action': True,
    'optim_alpha': 0.99,
    'optim_eps': 1e-05,
    'optimizer': 'adam',
    'per_alpha': 0.6,
    'per_beta': 0.4,
    'q_lambda': True,
    'repeat_id': 1,
    'return_priority': False,
    'rnn_hidden_dim': 64,
    'run': 'default',
    'runner': 'parallel',
    'runner_log_interval': 80000,
    'save_model': True,
    'save_model_interval': 50000,
    'save_replay': False,
    'seed': 1,
    't_max': 10050000,
    'target_update_interval': 200,
    'td_lambda': 0.6,
    'test_greedy': True,
    'test_interval': 80000,
    'test_nepisode': 30,
    'use_cuda': False,
    'use_layer_norm': False,
    'use_orthogonal': False,
    'use_per': False,
    'use_tensorboard': True,
    'use_wandb': False}

[DEBUG 15:37:43] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 15:37:43] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 15:37:43] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 15:37:43] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 15:37:43] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
[DEBUG 15:37:43] git.util Failed checking if running in CYGWIN due to: FileNotFoundError(2, 'No such file or directory')
Mixer Size: 
60.417K
[INFO 15:37:44] my_main Beginning training for 10050000 timesteps
[INFO 15:37:44] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59131 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-66keu9c3/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:44] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59130 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-il_nedqn/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:44] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59132 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-eeymls16/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:44] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59139 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-dyxomnz7/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:44] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59140 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-gdjxt7tn/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:44] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59138 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-k33suxr_/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:44] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 0, running: True
[INFO 15:37:44] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 0, running: True
[INFO 15:37:44] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 0, running: True
[INFO 15:37:44] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 0, running: True
[INFO 15:37:44] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 0, running: True
[INFO 15:37:44] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 0, running: True
[INFO 15:37:45] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 1, running: True
[INFO 15:37:45] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 1, running: True
[INFO 15:37:45] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 1, running: True
[INFO 15:37:45] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 1, running: True
[INFO 15:37:45] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 1, running: True
[INFO 15:37:45] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 1, running: True
[INFO 15:37:46] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 2, running: True
[INFO 15:37:46] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 2, running: True
[INFO 15:37:46] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 2, running: True
[INFO 15:37:46] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 2, running: True
[INFO 15:37:46] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 2, running: True
[INFO 15:37:46] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 2, running: True
[INFO 15:37:47] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 3, running: True
[INFO 15:37:47] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 3, running: True
[INFO 15:37:47] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 3, running: True
[INFO 15:37:47] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 3, running: True
[INFO 15:37:47] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 3, running: True
[INFO 15:37:47] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 3, running: True
[INFO 15:37:48] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 4, running: True
[INFO 15:37:48] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 4, running: True
[INFO 15:37:48] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 4, running: True
[INFO 15:37:48] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 4, running: True
[INFO 15:37:48] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 4, running: True
[INFO 15:37:48] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 4, running: True
[INFO 15:37:49] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 5, running: True
[INFO 15:37:49] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 5, running: True
[INFO 15:37:49] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 5, running: True
[INFO 15:37:49] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 5, running: True
[INFO 15:37:49] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 5, running: True
[INFO 15:37:49] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 5, running: True
[INFO 15:37:50] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 6, running: True
[INFO 15:37:50] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 6, running: True
[INFO 15:37:50] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 6, running: True
[INFO 15:37:50] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 6, running: True
[INFO 15:37:50] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 6, running: True
[INFO 15:37:50] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 6, running: True
[INFO 15:37:51] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 7, running: True
[INFO 15:37:51] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 7, running: True
[INFO 15:37:51] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 7, running: True
[INFO 15:37:51] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 7, running: True
[INFO 15:37:51] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 7, running: True
[INFO 15:37:51] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 7, running: True
[INFO 15:37:52] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 8, running: True
[INFO 15:37:52] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 8, running: True
[INFO 15:37:52] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 8, running: True
[INFO 15:37:52] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 8, running: True
[INFO 15:37:52] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 8, running: True
[INFO 15:37:52] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 8, running: True
[INFO 15:37:53] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 9, running: True
[INFO 15:37:53] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 9, running: True
[INFO 15:37:53] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 9, running: True
[INFO 15:37:53] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 9, running: True
[INFO 15:37:53] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 9, running: True
[INFO 15:37:53] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 9, running: True
[INFO 15:37:54] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 10, running: True
[INFO 15:37:54] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 10, running: True
[INFO 15:37:54] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 10, running: True
[INFO 15:37:54] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 10, running: True
[INFO 15:37:54] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 10, running: True
[INFO 15:37:54] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 10, running: True
[INFO 15:37:55] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 11, running: True
[INFO 15:37:55] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 11, running: True
[INFO 15:37:55] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 11, running: True
[INFO 15:37:55] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 11, running: True
[INFO 15:37:55] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 11, running: True
[INFO 15:37:55] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 11, running: True
[INFO 15:37:56] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 12, running: True
[INFO 15:37:56] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 12, running: True
[INFO 15:37:56] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 12, running: True
[INFO 15:37:56] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 12, running: True
[INFO 15:37:56] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 12, running: True
[INFO 15:37:56] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 12, running: True
[INFO 15:37:57] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 13, running: True
[INFO 15:37:57] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 13, running: True
[INFO 15:37:57] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 13, running: True
[INFO 15:37:57] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 13, running: True
[INFO 15:37:57] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 13, running: True
[INFO 15:37:57] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 13, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59132/sc2api, attempt: 14, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59131/sc2api, attempt: 14, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59140/sc2api, attempt: 14, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59139/sc2api, attempt: 14, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59138/sc2api, attempt: 14, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59130/sc2api, attempt: 14, running: True
[INFO 15:37:58] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59424 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-5vt4e_s4/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 0, running: True
[INFO 15:37:58] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59426 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-7iyvifyh/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:58] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59427 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-6rrxgnr4/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:58] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59428 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-iufl7du2/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:58] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59429 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-am2cnhgy/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 0, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 0, running: True
[INFO 15:37:58] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 59430 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-iq5yqzv1/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 0, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 0, running: True
[INFO 15:37:58] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 0, running: True
[INFO 15:37:59] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 1, running: True
[INFO 15:37:59] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 1, running: True
[INFO 15:37:59] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 1, running: True
[INFO 15:37:59] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 1, running: True
[INFO 15:37:59] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 1, running: True
[INFO 15:37:59] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 1, running: True
[INFO 15:38:00] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 2, running: True
[INFO 15:38:00] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 2, running: True
[INFO 15:38:00] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 2, running: True
[INFO 15:38:00] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 2, running: True
[INFO 15:38:00] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 2, running: True
[INFO 15:38:00] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 2, running: True
[INFO 15:38:01] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 3, running: True
[INFO 15:38:01] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 3, running: True
[INFO 15:38:01] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 3, running: True
[INFO 15:38:01] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 3, running: True
[INFO 15:38:01] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 3, running: True
[INFO 15:38:01] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 3, running: True
[INFO 15:38:02] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 4, running: True
[INFO 15:38:02] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 4, running: True
[INFO 15:38:02] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 4, running: True
[INFO 15:38:02] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 4, running: True
[INFO 15:38:02] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 4, running: True
[INFO 15:38:02] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 4, running: True
[INFO 15:38:03] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 5, running: True
[INFO 15:38:03] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 5, running: True
[INFO 15:38:03] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 5, running: True
[INFO 15:38:03] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 5, running: True
[INFO 15:38:03] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 5, running: True
[INFO 15:38:03] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 5, running: True
[INFO 15:38:04] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 6, running: True
[INFO 15:38:04] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 6, running: True
[INFO 15:38:04] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 6, running: True
[INFO 15:38:05] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 6, running: True
[INFO 15:38:05] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 6, running: True
[INFO 15:38:05] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 6, running: True
[INFO 15:38:05] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 7, running: True
[INFO 15:38:06] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 7, running: True
[INFO 15:38:06] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 7, running: True
[INFO 15:38:06] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 7, running: True
[INFO 15:38:06] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 7, running: True
[INFO 15:38:06] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 7, running: True
[INFO 15:38:06] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 8, running: True
[INFO 15:38:07] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 8, running: True
[INFO 15:38:07] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 8, running: True
[INFO 15:38:07] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 8, running: True
[INFO 15:38:07] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 8, running: True
[INFO 15:38:07] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 8, running: True
[INFO 15:38:07] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 9, running: True
[INFO 15:38:08] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 9, running: True
[INFO 15:38:08] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 9, running: True
[INFO 15:38:08] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 9, running: True
[INFO 15:38:08] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 9, running: True
[INFO 15:38:08] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 9, running: True
[INFO 15:38:08] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 10, running: True
[INFO 15:38:09] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 10, running: True
[INFO 15:38:09] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 10, running: True
[INFO 15:38:09] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 10, running: True
[INFO 15:38:09] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 10, running: True
[INFO 15:38:09] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 10, running: True
[INFO 15:38:09] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 11, running: True
[INFO 15:38:10] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 11, running: True
[INFO 15:38:10] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 11, running: True
[INFO 15:38:10] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 11, running: True
[INFO 15:38:10] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 11, running: True
[INFO 15:38:10] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 11, running: True
[INFO 15:38:10] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 12, running: True
[INFO 15:38:11] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 12, running: True
[INFO 15:38:11] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 12, running: True
[INFO 15:38:11] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 12, running: True
[INFO 15:38:11] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 12, running: True
[INFO 15:38:11] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 12, running: True
[INFO 15:38:12] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 13, running: True
[INFO 15:38:12] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 13, running: True
[INFO 15:38:12] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 13, running: True
[INFO 15:38:12] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 13, running: True
[INFO 15:38:12] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 13, running: True
[INFO 15:38:12] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 13, running: True
[INFO 15:38:13] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 14, running: True
[INFO 15:38:13] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 14, running: True
[INFO 15:38:13] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 14, running: True
[INFO 15:38:13] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 14, running: True
[INFO 15:38:13] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 14, running: True
[INFO 15:38:13] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 14, running: True
[INFO 15:38:14] absl Connecting to: ws://127.0.0.1:59424/sc2api, attempt: 15, running: True
[INFO 15:38:14] absl Connecting to: ws://127.0.0.1:59429/sc2api, attempt: 15, running: True
[INFO 15:38:14] absl Connecting to: ws://127.0.0.1:59426/sc2api, attempt: 15, running: True
[INFO 15:38:14] absl Connecting to: ws://127.0.0.1:59427/sc2api, attempt: 15, running: True
[INFO 15:38:14] absl Connecting to: ws://127.0.0.1:59430/sc2api, attempt: 15, running: True
[INFO 15:38:14] absl Connecting to: ws://127.0.0.1:59428/sc2api, attempt: 15, running: True
2025-07-17 15:41:32.967 SC2[45974:680015] error messaging the mach port for IMKCFRunLoopWakeUpReliable
/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/components/episode_buffer.py:103: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /Users/<USER>/work/_temp/anaconda/conda-bld/pytorch_1729647065806/work/torch/csrc/utils/tensor_new.cpp:281.)
  v = th.tensor(v, dtype=dtype, device=self.device)
/Users/<USER>/Developer/多智能体短学期/SMAC/pymarl2-smac-hard/src/components/episode_buffer.py:103: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).
  v = th.tensor(v, dtype=dtype, device=self.device)
[INFO 15:42:43] my_main t_env: 299 / 10050000
[INFO 15:42:43] my_main Estimated time left: 10 hours, 24 minutes, 8 seconds. Time passed: 4 minutes, 59 seconds
2025-07-17 15:43:37.742 SC2[45976:680020] error messaging the mach port for IMKCFRunLoopWakeUpReliable
[INFO 15:44:50] my_main Saving models to results/models/qmix_env=8_adam_td_lambda__2025-07-17_15-37-40/299
[INFO 15:45:09] my_main Recent Stats | t_env:        589 | Episode:       12
battle_won_mean:           0.0000	dead_allies_mean:          8.0000	dead_enemies_mean:         0.0000	ep_length_mean:           49.8333
epsilon:                   1.0000	return_mean:               3.6509	return_std:                0.2791	test_battle_won_mean:      0.0000
test_dead_allies_mean:     8.0000	test_dead_enemies_mean:    1.1333	test_ep_length_mean:      61.1667	test_return_mean:          4.8789
test_return_std:           1.2811	
[INFO 15:45:57] my_main Recent Stats | t_env:       1199 | Episode:       24

[INFO 15:46:46] my_main Recent Stats | t_env:       1790 | Episode:       36

[INFO 15:47:39] my_main Recent Stats | t_env:       2374 | Episode:       48

[INFO 15:48:35] my_main Recent Stats | t_env:       2979 | Episode:       60

[INFO 15:49:10] my_main Recent Stats | t_env:       3576 | Episode:       72

[INFO 15:49:42] my_main Recent Stats | t_env:       4176 | Episode:       84

[INFO 15:50:12] my_main Recent Stats | t_env:       4778 | Episode:       96

[INFO 15:50:35] my_main Recent Stats | t_env:       5360 | Episode:      108

[INFO 15:50:57] my_main Recent Stats | t_env:       5936 | Episode:      120

[INFO 15:51:25] my_main Recent Stats | t_env:       6524 | Episode:      132
grad_norm:                 4.7231	loss_td:                   0.5764	q_taken_mean:              0.0043	target_mean:               0.1168
td_error_abs:              0.5764	
[INFO 15:51:55] my_main Recent Stats | t_env:       7112 | Episode:      144

[INFO 15:52:21] my_main Recent Stats | t_env:       7725 | Episode:      156

[INFO 15:52:51] my_main Recent Stats | t_env:       8339 | Episode:      168

[INFO 15:53:18] my_main Recent Stats | t_env:       8929 | Episode:      180

[INFO 15:53:44] my_main Recent Stats | t_env:       9539 | Episode:      192

[INFO 15:54:06] my_main Recent Stats | t_env:      10115 | Episode:      204

[INFO 15:54:24] my_main Updated target network
[INFO 15:54:35] my_main Recent Stats | t_env:      10692 | Episode:      216

[INFO 15:55:05] my_main Recent Stats | t_env:      11292 | Episode:      228

[INFO 15:55:43] my_main Recent Stats | t_env:      11924 | Episode:      240

[INFO 15:56:11] my_main Recent Stats | t_env:      12521 | Episode:      252

[INFO 15:56:44] my_main Recent Stats | t_env:      13132 | Episode:      264

[INFO 15:57:09] my_main Recent Stats | t_env:      13714 | Episode:      276

[INFO 15:57:35] my_main Recent Stats | t_env:      14301 | Episode:      288

[INFO 15:57:59] my_main Recent Stats | t_env:      14905 | Episode:      300

[INFO 15:58:23] my_main Recent Stats | t_env:      15498 | Episode:      312

[INFO 15:58:58] my_main Recent Stats | t_env:      16084 | Episode:      324

[INFO 15:59:28] my_main Recent Stats | t_env:      16682 | Episode:      336

[INFO 16:00:02] my_main Recent Stats | t_env:      17271 | Episode:      348

[INFO 16:00:31] my_main Recent Stats | t_env:      17855 | Episode:      360

[INFO 16:00:57] my_main Recent Stats | t_env:      18442 | Episode:      372

[INFO 16:01:28] my_main Recent Stats | t_env:      19001 | Episode:      384

[INFO 16:01:56] my_main Recent Stats | t_env:      19567 | Episode:      396

[INFO 16:02:21] my_main Recent Stats | t_env:      20145 | Episode:      408

[INFO 16:02:38] my_main Updated target network
[INFO 16:02:58] my_main Recent Stats | t_env:      20746 | Episode:      420

[INFO 16:03:31] my_main Recent Stats | t_env:      21333 | Episode:      432

[INFO 16:04:07] my_main Recent Stats | t_env:      21927 | Episode:      444

[INFO 16:04:34] my_main Recent Stats | t_env:      22495 | Episode:      456

[INFO 16:05:05] my_main Recent Stats | t_env:      23077 | Episode:      468

[INFO 16:05:32] my_main Recent Stats | t_env:      23657 | Episode:      480

[INFO 16:06:05] my_main Recent Stats | t_env:      24228 | Episode:      492

[INFO 16:06:34] my_main Recent Stats | t_env:      24805 | Episode:      504

[INFO 16:07:07] my_main Recent Stats | t_env:      25395 | Episode:      516

[INFO 16:07:43] my_main Recent Stats | t_env:      26004 | Episode:      528

[INFO 16:08:13] my_main Recent Stats | t_env:      26613 | Episode:      540

[INFO 16:08:40] my_main Recent Stats | t_env:      27197 | Episode:      552

[INFO 16:09:08] my_main Recent Stats | t_env:      27802 | Episode:      564

[INFO 16:09:33] my_main Recent Stats | t_env:      28398 | Episode:      576

[INFO 16:10:03] my_main Recent Stats | t_env:      28997 | Episode:      588

[INFO 16:10:31] my_main Recent Stats | t_env:      29611 | Episode:      600

[INFO 16:10:55] my_main Recent Stats | t_env:      30192 | Episode:      612

[INFO 16:11:08] my_main Updated target network
[INFO 16:11:23] my_main Recent Stats | t_env:      30803 | Episode:      624

[INFO 16:11:52] my_main Recent Stats | t_env:      31402 | Episode:      636

[INFO 16:12:26] my_main Recent Stats | t_env:      32018 | Episode:      648

[INFO 16:13:01] my_main Recent Stats | t_env:      32619 | Episode:      660

[INFO 16:13:32] my_main Recent Stats | t_env:      33251 | Episode:      672

[INFO 16:14:04] my_main Recent Stats | t_env:      33858 | Episode:      684

[INFO 16:14:45] my_main Recent Stats | t_env:      34444 | Episode:      696

[INFO 16:15:40] my_main Recent Stats | t_env:      35024 | Episode:      708

[INFO 16:16:32] my_main Recent Stats | t_env:      35591 | Episode:      720

[INFO 16:17:27] my_main Recent Stats | t_env:      36155 | Episode:      732

[INFO 16:18:19] my_main Recent Stats | t_env:      36730 | Episode:      744

[INFO 16:18:52] my_main Recent Stats | t_env:      37321 | Episode:      756

[INFO 16:19:19] my_main Recent Stats | t_env:      37919 | Episode:      768

[INFO 16:19:49] my_main Recent Stats | t_env:      38511 | Episode:      780

[INFO 16:20:20] my_main Recent Stats | t_env:      39098 | Episode:      792

[INFO 16:20:48] my_main Recent Stats | t_env:      39674 | Episode:      804

[INFO 16:21:14] my_main Recent Stats | t_env:      40236 | Episode:      816

[INFO 16:21:27] my_main Updated target network
[INFO 16:21:39] my_main Recent Stats | t_env:      40793 | Episode:      828

[INFO 16:22:08] my_main Recent Stats | t_env:      41366 | Episode:      840

[INFO 16:22:45] my_main Recent Stats | t_env:      41951 | Episode:      852

[INFO 16:23:19] my_main Recent Stats | t_env:      42535 | Episode:      864

[INFO 16:23:57] my_main Recent Stats | t_env:      43140 | Episode:      876

[INFO 16:24:32] my_main Recent Stats | t_env:      43724 | Episode:      888

[INFO 16:24:59] my_main Recent Stats | t_env:      44292 | Episode:      900

[INFO 16:25:36] my_main Recent Stats | t_env:      44878 | Episode:      912

[INFO 16:26:13] my_main Recent Stats | t_env:      45488 | Episode:      924

[INFO 16:26:40] my_main Recent Stats | t_env:      46066 | Episode:      936

[INFO 16:27:07] my_main Recent Stats | t_env:      46668 | Episode:      948

[INFO 16:27:33] my_main Recent Stats | t_env:      47269 | Episode:      960

[INFO 16:27:59] my_main Recent Stats | t_env:      47866 | Episode:      972

[INFO 16:28:28] my_main Recent Stats | t_env:      48459 | Episode:      984

[INFO 16:29:23] absl Shutdown gracefully.
[INFO 16:29:23] absl Shutdown with return code: 1
[INFO 16:29:23] absl Shutdown gracefully.
[INFO 16:29:23] absl Shutdown with return code: -15
[INFO 16:29:23] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 53582 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-bdhl53iu/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 16:29:23] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 0, running: True
[INFO 16:29:24] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 1, running: True
[INFO 16:29:25] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 2, running: True
[INFO 16:29:26] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 3, running: True
[INFO 16:29:27] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 4, running: True
[INFO 16:29:28] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 5, running: True
[INFO 16:29:29] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 6, running: True
[INFO 16:29:30] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 7, running: True
[INFO 16:29:31] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 8, running: True
[INFO 16:29:32] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 9, running: True
[INFO 16:29:33] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 10, running: True
[INFO 16:29:34] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 11, running: True
[INFO 16:29:35] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 12, running: True
[INFO 16:29:36] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 13, running: True
[INFO 16:29:37] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 14, running: True
[INFO 16:29:38] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 15, running: True
[INFO 16:29:39] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 16, running: True
[INFO 16:29:40] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 17, running: True
[INFO 16:29:41] absl Connecting to: ws://127.0.0.1:53582/sc2api, attempt: 18, running: True
[INFO 16:29:41] absl Launching SC2: /Applications/StarCraft II/Versions/Base94137/SC2.app/Contents/MacOS/SC2 -listen 127.0.0.1 -port 53679 -dataDir /Applications/StarCraft II/ -tempDir /var/folders/53/4vgm7y757_jd0gnfljyg8g100000gn/T/sc-bu5wnn9n/ -displayMode 0 -windowwidth 640 -windowheight 480 -windowx 50 -windowy 50
[INFO 16:29:41] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 0, running: True
[INFO 16:29:42] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 1, running: True
[INFO 16:29:43] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 2, running: True
[INFO 16:29:44] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 3, running: True
[INFO 16:29:45] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 4, running: True
[INFO 16:29:46] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 5, running: True
[INFO 16:29:47] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 6, running: True
[INFO 16:29:48] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 7, running: True
[INFO 16:29:49] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 8, running: True
[INFO 16:29:50] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 9, running: True
[INFO 16:29:51] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 10, running: True
[INFO 16:29:52] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 11, running: True
[INFO 16:29:53] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 12, running: True
[INFO 16:29:54] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 13, running: True
[INFO 16:29:55] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 14, running: True
[INFO 16:29:56] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 15, running: True
[INFO 16:29:57] absl Connecting to: ws://127.0.0.1:53679/sc2api, attempt: 16, running: True
[INFO 16:30:15] my_main Recent Stats | t_env:      49052 | Episode:      996

[INFO 16:30:44] my_main Recent Stats | t_env:      49670 | Episode:     1008

[INFO 16:31:10] my_main Recent Stats | t_env:      50259 | Episode:     1020

[INFO 16:31:25] my_main Updated target network
[INFO 16:31:25] my_main Saving models to results/models/qmix_env=8_adam_td_lambda__2025-07-17_15-37-40/50555
[INFO 16:31:42] my_main Recent Stats | t_env:      50857 | Episode:     1032

[INFO 16:32:09] my_main Recent Stats | t_env:      51478 | Episode:     1044

[INFO 16:32:42] my_main Recent Stats | t_env:      52107 | Episode:     1056

[INFO 16:33:11] my_main Recent Stats | t_env:      52738 | Episode:     1068

[INFO 16:33:36] my_main Recent Stats | t_env:      53320 | Episode:     1080

[INFO 16:34:05] my_main Recent Stats | t_env:      53947 | Episode:     1092

[INFO 16:34:31] my_main Recent Stats | t_env:      54538 | Episode:     1104

[INFO 16:34:55] my_main Recent Stats | t_env:      55092 | Episode:     1116

[INFO 16:35:18] my_main Recent Stats | t_env:      55670 | Episode:     1128

[INFO 16:35:44] my_main Recent Stats | t_env:      56297 | Episode:     1140

[INFO 16:36:09] my_main Recent Stats | t_env:      56883 | Episode:     1152

[INFO 16:36:34] my_main Recent Stats | t_env:      57439 | Episode:     1164

[INFO 16:36:59] my_main Recent Stats | t_env:      58022 | Episode:     1176

[INFO 16:37:24] my_main Recent Stats | t_env:      58602 | Episode:     1188

[INFO 16:37:49] my_main Recent Stats | t_env:      59196 | Episode:     1200

[INFO 16:38:14] my_main Recent Stats | t_env:      59778 | Episode:     1212

[INFO 16:38:40] my_main Recent Stats | t_env:      60384 | Episode:     1224

[INFO 16:38:50] my_main Updated target network
[INFO 16:39:01] my_main Recent Stats | t_env:      60929 | Episode:     1236

[INFO 16:39:25] my_main Recent Stats | t_env:      61502 | Episode:     1248

[INFO 16:39:51] my_main Recent Stats | t_env:      62086 | Episode:     1260

[INFO 16:40:16] my_main Recent Stats | t_env:      62655 | Episode:     1272

[INFO 16:40:39] my_main Recent Stats | t_env:      63217 | Episode:     1284

[INFO 16:41:07] my_main Recent Stats | t_env:      63821 | Episode:     1296

[INFO 16:41:42] my_main Recent Stats | t_env:      64403 | Episode:     1308

[INFO 16:42:20] my_main Recent Stats | t_env:      64987 | Episode:     1320

[INFO 16:42:56] my_main Recent Stats | t_env:      65552 | Episode:     1332

[INFO 16:43:33] my_main Recent Stats | t_env:      66170 | Episode:     1344

[INFO 16:44:10] my_main Recent Stats | t_env:      66743 | Episode:     1356

[INFO 16:44:46] my_main Recent Stats | t_env:      67349 | Episode:     1368

[INFO 16:45:28] my_main Recent Stats | t_env:      67942 | Episode:     1380

[INFO 16:46:09] my_main Recent Stats | t_env:      68558 | Episode:     1392

[INFO 16:46:47] my_main Recent Stats | t_env:      69157 | Episode:     1404

[INFO 16:47:22] my_main Recent Stats | t_env:      69748 | Episode:     1416

[INFO 16:47:59] my_main Recent Stats | t_env:      70332 | Episode:     1428

[INFO 16:48:19] my_main Updated target network
[INFO 16:48:37] my_main Recent Stats | t_env:      70982 | Episode:     1440

Blizzard Error Report ID: 4987545E-FFD5-491C-9DD6-B68D69D5A2D2
[INFO 16:49:21] my_main Recent Stats | t_env:      71609 | Episode:     1452

[INFO 16:50:10] my_main Recent Stats | t_env:      72220 | Episode:     1464

[INFO 16:51:01] my_main Recent Stats | t_env:      72855 | Episode:     1476

[INFO 16:51:51] my_main Recent Stats | t_env:      73481 | Episode:     1488

[INFO 16:52:42] my_main Recent Stats | t_env:      74093 | Episode:     1500

[INFO 16:53:35] my_main Recent Stats | t_env:      74730 | Episode:     1512

[INFO 16:54:26] my_main Recent Stats | t_env:      75371 | Episode:     1524

[INFO 16:55:19] my_main Recent Stats | t_env:      76008 | Episode:     1536

[INFO 16:56:07] my_main Recent Stats | t_env:      76623 | Episode:     1548

[INFO 16:56:54] my_main Recent Stats | t_env:      77263 | Episode:     1560

[INFO 16:57:41] my_main Recent Stats | t_env:      77870 | Episode:     1572

[INFO 16:58:29] my_main Recent Stats | t_env:      78476 | Episode:     1584

[INFO 16:59:18] my_main Recent Stats | t_env:      79114 | Episode:     1596

[INFO 17:00:07] my_main Recent Stats | t_env:      79757 | Episode:     1608

[INFO 17:00:55] my_main t_env: 80355 / 10050000
[INFO 17:00:55] my_main Estimated time left: 6 days, 18 hours, 16 minutes, 58 seconds. Time passed: 1 hours, 23 minutes, 10 seconds
[INFO 17:03:08] my_main Recent Stats | t_env:      80355 | Episode:     1620
battle_won_mean:           0.0000	dead_allies_mean:          7.9950	dead_enemies_mean:         0.4950	ep_length_mean:           49.6010
epsilon:                   0.2395	return_mean:               5.0364	return_std:                1.3824	test_battle_won_mean:      0.0000
test_dead_allies_mean:     8.0000	test_dead_enemies_mean:    2.7333	test_ep_length_mean:      54.7333	test_return_mean:          8.0736
test_return_std:           1.2909	
[INFO 17:03:55] my_main Recent Stats | t_env:      80975 | Episode:     1632

[INFO 17:04:22] my_main Updated target network
[INFO 17:04:44] my_main Recent Stats | t_env:      81607 | Episode:     1644

[INFO 17:05:35] my_main Recent Stats | t_env:      82264 | Episode:     1656

[INFO 17:06:24] my_main Recent Stats | t_env:      82902 | Episode:     1668

[INFO 17:07:12] my_main Recent Stats | t_env:      83511 | Episode:     1680

[INFO 17:07:59] my_main Recent Stats | t_env:      84122 | Episode:     1692

[INFO 17:08:47] my_main Recent Stats | t_env:      84746 | Episode:     1704

[INFO 17:09:39] my_main Recent Stats | t_env:      85385 | Episode:     1716

[INFO 17:10:26] my_main Recent Stats | t_env:      85998 | Episode:     1728

[INFO 17:11:14] my_main Recent Stats | t_env:      86606 | Episode:     1740
grad_norm:                 0.7572	loss_td:                   0.0249	q_taken_mean:              0.3015	target_mean:               0.3119
td_error_abs:              0.0249	
[INFO 17:12:07] my_main Recent Stats | t_env:      87247 | Episode:     1752

[INFO 17:12:56] my_main Recent Stats | t_env:      87878 | Episode:     1764

[INFO 17:13:47] my_main Recent Stats | t_env:      88521 | Episode:     1776

[INFO 17:14:35] my_main Recent Stats | t_env:      89126 | Episode:     1788

[INFO 17:15:30] my_main Recent Stats | t_env:      89760 | Episode:     1800

[INFO 17:16:24] my_main Recent Stats | t_env:      90385 | Episode:     1812

[INFO 17:17:12] my_main Recent Stats | t_env:      90972 | Episode:     1824

[INFO 17:18:26] my_main Recent Stats | t_env:      91572 | Episode:     1836

[INFO 17:19:01] my_main Updated target network
[INFO 17:19:26] my_main Recent Stats | t_env:      92213 | Episode:     1848

[INFO 17:20:21] my_main Recent Stats | t_env:      92845 | Episode:     1860

[INFO 17:21:12] my_main Recent Stats | t_env:      93448 | Episode:     1872

[INFO 17:22:12] my_main Recent Stats | t_env:      94064 | Episode:     1884

[INFO 17:23:08] my_main Recent Stats | t_env:      94638 | Episode:     1896

[INFO 17:24:10] my_main Recent Stats | t_env:      95292 | Episode:     1908

[INFO 17:24:57] my_main Recent Stats | t_env:      95897 | Episode:     1920

[INFO 17:25:47] my_main Recent Stats | t_env:      96519 | Episode:     1932

[INFO 17:26:38] my_main Recent Stats | t_env:      97157 | Episode:     1944

[INFO 17:27:27] my_main Recent Stats | t_env:      97793 | Episode:     1956

[INFO 17:28:18] my_main Recent Stats | t_env:      98445 | Episode:     1968

[INFO 17:29:11] my_main Recent Stats | t_env:      99119 | Episode:     1980

[INFO 17:30:04] my_main Recent Stats | t_env:      99791 | Episode:     1992

[INFO 17:30:57] my_main Recent Stats | t_env:     100459 | Episode:     2004

[INFO 17:31:24] my_main Saving models to results/models/qmix_env=8_adam_td_lambda__2025-07-17_15-37-40/100793
[INFO 17:31:49] my_main Recent Stats | t_env:     101109 | Episode:     2016

[INFO 17:32:53] my_main Recent Stats | t_env:     101821 | Episode:     2028

[INFO 17:33:45] my_main Recent Stats | t_env:     102488 | Episode:     2040

[INFO 17:34:14] my_main Updated target network
[INFO 17:34:38] my_main Recent Stats | t_env:     103186 | Episode:     2052

[INFO 17:35:36] my_main Recent Stats | t_env:     103893 | Episode:     2064

[INFO 17:36:28] my_main Recent Stats | t_env:     104543 | Episode:     2076

[INFO 17:37:16] my_main Recent Stats | t_env:     105172 | Episode:     2088

[INFO 17:38:09] my_main Recent Stats | t_env:     105860 | Episode:     2100

[INFO 17:39:03] my_main Recent Stats | t_env:     106509 | Episode:     2112

[INFO 17:39:52] my_main Recent Stats | t_env:     107123 | Episode:     2124

[INFO 17:40:43] my_main Recent Stats | t_env:     107745 | Episode:     2136

[INFO 17:41:31] my_main Recent Stats | t_env:     108349 | Episode:     2148

[INFO 17:42:20] my_main Recent Stats | t_env:     108982 | Episode:     2160

[INFO 17:43:10] my_main Recent Stats | t_env:     109621 | Episode:     2172

[INFO 17:43:57] my_main Recent Stats | t_env:     110213 | Episode:     2184

[INFO 17:44:54] my_main Recent Stats | t_env:     110838 | Episode:     2196

[INFO 17:45:43] my_main Recent Stats | t_env:     111444 | Episode:     2208

[INFO 17:46:32] my_main Recent Stats | t_env:     112043 | Episode:     2220

[INFO 17:47:21] my_main Recent Stats | t_env:     112650 | Episode:     2232

[INFO 17:48:11] my_main Recent Stats | t_env:     113240 | Episode:     2244

[INFO 17:48:38] my_main Updated target network
[INFO 17:49:05] my_main Recent Stats | t_env:     113876 | Episode:     2256

[INFO 17:50:03] my_main Recent Stats | t_env:     114522 | Episode:     2268

[INFO 17:50:54] my_main Recent Stats | t_env:     115145 | Episode:     2280

[INFO 17:51:46] my_main Recent Stats | t_env:     115787 | Episode:     2292

[INFO 17:52:44] my_main Recent Stats | t_env:     116451 | Episode:     2304

[INFO 17:53:36] my_main Recent Stats | t_env:     117088 | Episode:     2316

[INFO 17:54:29] my_main Recent Stats | t_env:     117732 | Episode:     2328

[INFO 17:55:16] my_main Recent Stats | t_env:     118349 | Episode:     2340

[INFO 17:56:07] my_main Recent Stats | t_env:     118969 | Episode:     2352

[INFO 17:56:53] my_main Recent Stats | t_env:     119572 | Episode:     2364

[INFO 17:57:40] my_main Recent Stats | t_env:     120162 | Episode:     2376

[INFO 17:58:26] my_main Recent Stats | t_env:     120756 | Episode:     2388

[INFO 17:59:22] my_main Recent Stats | t_env:     121424 | Episode:     2400

[INFO 18:00:18] my_main Recent Stats | t_env:     122083 | Episode:     2412

[INFO 18:01:15] my_main Recent Stats | t_env:     122763 | Episode:     2424

[INFO 18:02:09] my_main Recent Stats | t_env:     123411 | Episode:     2436

[INFO 18:03:05] my_main Recent Stats | t_env:     124043 | Episode:     2448

[INFO 18:03:30] my_main Updated target network
[INFO 18:03:58] my_main Recent Stats | t_env:     124685 | Episode:     2460

[INFO 18:04:50] my_main Recent Stats | t_env:     125295 | Episode:     2472

[INFO 18:05:39] my_main Recent Stats | t_env:     125893 | Episode:     2484

[INFO 18:06:29] my_main Recent Stats | t_env:     126532 | Episode:     2496

[INFO 18:07:21] my_main Recent Stats | t_env:     127170 | Episode:     2508

[INFO 18:08:15] my_main Recent Stats | t_env:     127837 | Episode:     2520

[INFO 18:09:10] my_main Recent Stats | t_env:     128497 | Episode:     2532

[INFO 18:10:11] my_main Recent Stats | t_env:     129167 | Episode:     2544

[INFO 18:11:07] my_main Recent Stats | t_env:     129822 | Episode:     2556

[INFO 18:11:56] my_main Recent Stats | t_env:     130434 | Episode:     2568

[INFO 18:12:46] my_main Recent Stats | t_env:     131043 | Episode:     2580

[INFO 18:13:40] my_main Recent Stats | t_env:     131683 | Episode:     2592

[INFO 18:14:29] my_main Recent Stats | t_env:     132277 | Episode:     2604

[INFO 18:15:21] my_main Recent Stats | t_env:     132901 | Episode:     2616

[INFO 18:16:17] my_main Recent Stats | t_env:     133575 | Episode:     2628

[INFO 18:17:08] my_main Recent Stats | t_env:     134229 | Episode:     2640

[INFO 18:18:07] my_main Recent Stats | t_env:     134937 | Episode:     2652

[INFO 18:18:36] my_main Updated target network
[INFO 18:19:05] my_main Recent Stats | t_env:     135626 | Episode:     2664

[INFO 18:20:02] my_main Recent Stats | t_env:     136314 | Episode:     2676

[INFO 18:20:55] my_main Recent Stats | t_env:     136964 | Episode:     2688

[INFO 18:21:54] my_main Recent Stats | t_env:     137616 | Episode:     2700

[INFO 18:23:21] my_main Recent Stats | t_env:     138305 | Episode:     2712

[INFO 18:24:12] my_main Recent Stats | t_env:     138945 | Episode:     2724

[INFO 18:25:01] my_main Recent Stats | t_env:     139560 | Episode:     2736

[INFO 18:26:04] my_main Recent Stats | t_env:     140242 | Episode:     2748

[INFO 18:27:03] my_main Recent Stats | t_env:     140881 | Episode:     2760

[INFO 18:27:56] my_main Recent Stats | t_env:     141469 | Episode:     2772

[INFO 18:28:55] my_main Recent Stats | t_env:     142068 | Episode:     2784

[INFO 18:29:55] my_main Recent Stats | t_env:     142723 | Episode:     2796

[INFO 18:30:55] my_main Recent Stats | t_env:     143343 | Episode:     2808

[INFO 18:32:02] my_main Recent Stats | t_env:     144005 | Episode:     2820

[INFO 18:33:16] my_main Recent Stats | t_env:     144628 | Episode:     2832

[INFO 18:34:09] my_main Recent Stats | t_env:     145235 | Episode:     2844

[INFO 18:35:05] my_main Recent Stats | t_env:     145861 | Episode:     2856

[INFO 18:35:28] my_main Updated target network
[INFO 18:35:58] my_main Recent Stats | t_env:     146479 | Episode:     2868

[INFO 18:36:49] my_main Recent Stats | t_env:     147078 | Episode:     2880

[INFO 18:37:51] my_main Recent Stats | t_env:     147677 | Episode:     2892

[INFO 18:38:45] my_main Recent Stats | t_env:     148287 | Episode:     2904

[INFO 18:39:36] my_main Recent Stats | t_env:     148888 | Episode:     2916

[INFO 18:40:44] my_main Recent Stats | t_env:     149485 | Episode:     2928

[INFO 18:41:43] my_main Recent Stats | t_env:     150115 | Episode:     2940

[INFO 18:42:39] my_main Recent Stats | t_env:     150764 | Episode:     2952

[INFO 18:43:04] my_main Saving models to results/models/qmix_env=8_adam_td_lambda__2025-07-17_15-37-40/151079
[INFO 18:43:29] my_main Recent Stats | t_env:     151389 | Episode:     2964

[INFO 18:44:23] my_main Recent Stats | t_env:     152017 | Episode:     2976

[INFO 18:45:18] my_main Recent Stats | t_env:     152652 | Episode:     2988

[INFO 18:46:14] my_main Recent Stats | t_env:     153337 | Episode:     3000

[INFO 18:47:06] my_main Recent Stats | t_env:     153990 | Episode:     3012

[INFO 18:47:53] my_main Recent Stats | t_env:     154599 | Episode:     3024

[INFO 18:48:44] my_main Recent Stats | t_env:     155236 | Episode:     3036

[INFO 18:49:30] my_main Recent Stats | t_env:     155848 | Episode:     3048

[INFO 18:50:22] my_main Recent Stats | t_env:     156482 | Episode:     3060

[INFO 18:50:46] my_main Updated target network
[INFO 18:51:08] my_main Recent Stats | t_env:     157084 | Episode:     3072

[INFO 18:52:03] my_main Recent Stats | t_env:     157742 | Episode:     3084

[INFO 18:52:55] my_main Recent Stats | t_env:     158384 | Episode:     3096

[INFO 18:53:49] my_main Recent Stats | t_env:     159003 | Episode:     3108

[INFO 18:54:40] my_main Recent Stats | t_env:     159624 | Episode:     3120

[INFO 18:55:57] my_main Recent Stats | t_env:     160248 | Episode:     3132

[INFO 18:56:18] my_main t_env: 160519 / 10050000
[INFO 18:56:18] my_main Estimated time left: 9 days, 21 hours, 15 minutes, 33 seconds. Time passed: 3 hours, 18 minutes, 34 seconds
[INFO 18:59:03] my_main Recent Stats | t_env:     160814 | Episode:     3144
battle_won_mean:           0.0000	dead_allies_mean:          8.0000	dead_enemies_mean:         1.7391	ep_length_mean:           52.8090
epsilon:                   0.0500	return_mean:               7.4036	return_std:                1.2572	test_battle_won_mean:      0.0000
test_dead_allies_mean:     8.0000	test_dead_enemies_mean:    2.2667	test_ep_length_mean:      50.1000	test_return_mean:          7.7118
test_return_std:           1.2658	
[INFO 19:00:14] my_main Recent Stats | t_env:     161446 | Episode:     3156

[INFO 19:01:17] my_main Recent Stats | t_env:     162082 | Episode:     3168

[INFO 19:02:14] my_main Recent Stats | t_env:     162683 | Episode:     3180

