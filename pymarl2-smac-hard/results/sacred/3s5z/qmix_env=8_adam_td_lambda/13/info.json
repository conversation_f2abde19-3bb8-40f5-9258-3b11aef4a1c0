{"battle_won_mean": [0.0, 0.0, 0.0], "battle_won_mean_T": [299, 80355, 160519], "dead_allies_mean": [8.0, 7.995043370508054, 8.0], "dead_allies_mean_T": [299, 80355, 160519], "dead_enemies_mean": [0.0, 0.4950433705080545, 1.7391304347826086], "dead_enemies_mean_T": [299, 80355, 160519], "ep_length_mean": [49.833333333333336, 49.60099132589839, 52.80895915678524], "ep_length_mean_T": [299, 80355, 160519], "episode": [12, 24, 36, 48, 60, 72, 84, 96, 108, 120, 132, 144, 156, 168, 180, 192, 204, 216, 228, 240, 252, 264, 276, 288, 300, 312, 324, 336, 348, 360, 372, 384, 396, 408, 420, 432, 444, 456, 468, 480, 492, 504, 516, 528, 540, 552, 564, 576, 588, 600, 612, 624, 636, 648, 660, 672, 684, 696, 708, 720, 732, 744, 756, 768, 780, 792, 804, 816, 828, 840, 852, 864, 876, 888, 900, 912, 924, 936, 948, 960, 972, 984, 996, 1008, 1020, 1032, 1044, 1056, 1068, 1080, 1092, 1104, 1116, 1128, 1140, 1152, 1164, 1176, 1188, 1200, 1212, 1224, 1236, 1248, 1260, 1272, 1284, 1296, 1308, 1320, 1332, 1344, 1356, 1368, 1380, 1392, 1404, 1416, 1428, 1440, 1452, 1464, 1476, 1488, 1500, 1512, 1524, 1536, 1548, 1560, 1572, 1584, 1596, 1608, 1620, 1632, 1644, 1656, 1668, 1680, 1692, 1704, 1716, 1728, 1740, 1752, 1764, 1776, 1788, 1800, 1812, 1824, 1836, 1848, 1860, 1872, 1884, 1896, 1908, 1920, 1932, 1944, 1956, 1968, 1980, 1992, 2004, 2016, 2028, 2040, 2052, 2064, 2076, 2088, 2100, 2112, 2124, 2136, 2148, 2160, 2172, 2184, 2196, 2208, 2220, 2232, 2244, 2256, 2268, 2280, 2292, 2304, 2316, 2328, 2340, 2352, 2364, 2376, 2388, 2400, 2412, 2424, 2436, 2448, 2460, 2472, 2484, 2496, 2508, 2520, 2532, 2544, 2556, 2568, 2580, 2592, 2604, 2616, 2628, 2640, 2652, 2664, 2676, 2688, 2700, 2712, 2724, 2736, 2748, 2760, 2772, 2784, 2796, 2808, 2820, 2832, 2844, 2856, 2868, 2880, 2892, 2904, 2916, 2928, 2940, 2952, 2964, 2976, 2988, 3000, 3012, 3024, 3036, 3048, 3060, 3072, 3084, 3096, 3108, 3120, 3132, 3144, 3156, 3168, 3180], "episode_T": [589, 1199, 1790, 2374, 2979, 3576, 4176, 4778, 5360, 5936, 6524, 7112, 7725, 8339, 8929, 9539, 10115, 10692, 11292, 11924, 12521, 13132, 13714, 14301, 14905, 15498, 16084, 16682, 17271, 17855, 18442, 19001, 19567, 20145, 20746, 21333, 21927, 22495, 23077, 23657, 24228, 24805, 25395, 26004, 26613, 27197, 27802, 28398, 28997, 29611, 30192, 30803, 31402, 32018, 32619, 33251, 33858, 34444, 35024, 35591, 36155, 36730, 37321, 37919, 38511, 39098, 39674, 40236, 40793, 41366, 41951, 42535, 43140, 43724, 44292, 44878, 45488, 46066, 46668, 47269, 47866, 48459, 49052, 49670, 50259, 50857, 51478, 52107, 52738, 53320, 53947, 54538, 55092, 55670, 56297, 56883, 57439, 58022, 58602, 59196, 59778, 60384, 60929, 61502, 62086, 62655, 63217, 63821, 64403, 64987, 65552, 66170, 66743, 67349, 67942, 68558, 69157, 69748, 70332, 70982, 71609, 72220, 72855, 73481, 74093, 74730, 75371, 76008, 76623, 77263, 77870, 78476, 79114, 79757, 80355, 80975, 81607, 82264, 82902, 83511, 84122, 84746, 85385, 85998, 86606, 87247, 87878, 88521, 89126, 89760, 90385, 90972, 91572, 92213, 92845, 93448, 94064, 94638, 95292, 95897, 96519, 97157, 97793, 98445, 99119, 99791, 100459, 101109, 101821, 102488, 103186, 103893, 104543, 105172, 105860, 106509, 107123, 107745, 108349, 108982, 109621, 110213, 110838, 111444, 112043, 112650, 113240, 113876, 114522, 115145, 115787, 116451, 117088, 117732, 118349, 118969, 119572, 120162, 120756, 121424, 122083, 122763, 123411, 124043, 124685, 125295, 125893, 126532, 127170, 127837, 128497, 129167, 129822, 130434, 131043, 131683, 132277, 132901, 133575, 134229, 134937, 135626, 136314, 136964, 137616, 138305, 138945, 139560, 140242, 140881, 141469, 142068, 142723, 143343, 144005, 144628, 145235, 145861, 146479, 147078, 147677, 148287, 148888, 149485, 150115, 150764, 151389, 152017, 152652, 153337, 153990, 154599, 155236, 155848, 156482, 157084, 157742, 158384, 159003, 159624, 160248, 160814, 161446, 162082, 162683], "epsilon": [1.0, 0.2394965000000001, 0.05], "epsilon_T": [299, 80355, 160519], "grad_norm": [{"py/reduce": [{"py/function": "torch._utils._rebuild_tensor_v2"}, {"py/tuple": [{"py/reduce": [{"py/function": "torch.storage._load_from_bytes"}, {"py/tuple": [{"py/b64": "gAKKCmz8nEb5IGqoUBkugAJN6QMugAJ9cQAoWBAAAABwcm90b2NvbF92ZXJzaW9ucQFN6QNYDQAAAGxpdHRsZV9lbmRpYW5xAohYCgAAAHR5cGVfc2l6ZXNxA31xBChYBQAAAHNob3J0cQVLAlgDAAAAaW50cQZLBFgEAAAAbG9uZ3EHSwR1dS6AAihYBwAAAHN0b3JhZ2VxAGN0b3JjaApGbG9hdFN0b3JhZ2UKcQFYCgAAADQ4NDY1NDA5NzZxAlgDAAAAY3B1cQNLAU50cQRRLoACXXEAWAoAAAA0ODQ2NTQwOTc2cQFhLgEAAAAAAAAA8SOXQA=="}]}]}, 0, {"py/tuple": []}, {"py/tuple": []}, false, {"py/reduce": [{"py/type": "collections.OrderedDict"}, {"py/tuple": []}, null, null, {"py/tuple": []}]}]}]}, {"py/reduce": [{"py/function": "torch._utils._rebuild_tensor_v2"}, {"py/tuple": [{"py/reduce": [{"py/function": "torch.storage._load_from_bytes"}, {"py/tuple": [{"py/b64": "gAKKCmz8nEb5IGqoUBkugAJN6QMugAJ9cQAoWBAAAABwcm90b2NvbF92ZXJzaW9ucQFN6QNYDQAAAGxpdHRsZV9lbmRpYW5xAohYCgAAAHR5cGVfc2l6ZXNxA31xBChYBQAAAHNob3J0cQVLAlgDAAAAaW50cQZLBFgEAAAAbG9uZ3EHSwR1dS6AAihYBwAAAHN0b3JhZ2VxAGN0b3JjaApGbG9hdFN0b3JhZ2UKcQFYCgAAADUxMTU2NTM5MzZxAlgDAAAAY3B1cQNLAU50cQRRLoACXXEAWAoAAAA1MTE1NjUzOTM2cQFhLgEAAAAAAAAAo9lBPw=="}]}]}, 0, {"py/tuple": []}, {"py/tuple": []}, false, {"py/reduce": [{"py/type": "collections.OrderedDict"}, {"py/tuple": []}, null, null, {"py/tuple": []}]}]}]}], "grad_norm_T": [6524, 86606], "loss_td": [0.5763669013977051, 0.024944711476564407], "loss_td_T": [6524, 86606], "q_taken_mean": [0.004294764494315529, 0.30149323950509654], "q_taken_mean_T": [6524, 86606], "return_mean": [{"dtype": "float64", "py/object": "numpy.float64", "value": 3.650938189845475}, {"dtype": "float64", "py/object": "numpy.float64", "value": 5.036410505756748}, {"dtype": "float64", "py/object": "numpy.float64", "value": 7.4035721452939995}], "return_mean_T": [299, 80355, 160519], "return_std": [{"dtype": "float64", "py/object": "numpy.float64", "value": 0.27911212693027}, {"dtype": "float64", "py/object": "numpy.float64", "value": 1.3824325380535103}, {"dtype": "float64", "py/object": "numpy.float64", "value": 1.2572243107727872}], "return_std_T": [299, 80355, 160519], "target_mean": [0.11683048389397427, 0.31190600597579876], "target_mean_T": [6524, 86606], "td_error_abs": [0.576366925066195, 0.024944712296708593], "td_error_abs_T": [6524, 86606], "test_battle_won_mean": [0.0, 0.0, 0.0], "test_battle_won_mean_T": [299, 80355, 160519], "test_dead_allies_mean": [8.0, 8.0, 8.0], "test_dead_allies_mean_T": [299, 80355, 160519], "test_dead_enemies_mean": [1.1333333333333333, 2.7333333333333334, 2.2666666666666666], "test_dead_enemies_mean_T": [299, 80355, 160519], "test_ep_length_mean": [61.166666666666664, 54.733333333333334, 50.1], "test_ep_length_mean_T": [299, 80355, 160519], "test_return_mean": [{"dtype": "float64", "py/object": "numpy.float64", "value": 4.878863134657838}, {"dtype": "float64", "py/object": "numpy.float64", "value": 8.073620309050773}, {"dtype": "float64", "py/object": "numpy.float64", "value": 7.711810154525386}], "test_return_mean_T": [299, 80355, 160519], "test_return_std": [{"dtype": "float64", "py/object": "numpy.float64", "value": 1.2811224389077904}, {"dtype": "float64", "py/object": "numpy.float64", "value": 1.2908574019941883}, {"dtype": "float64", "py/object": "numpy.float64", "value": 1.2657829907347657}], "test_return_std_T": [299, 80355, 160519]}