from smac_hard.env import StarCraft2Env
from submission.team1.red import PolicyRed
from submission.team1.blue import PolicyBlue

import numpy as np

def main():
    # 初始化 StarCraft2 环境
    # map_name 应该与你训练模型时使用的地图一致
    # mode='multi' 表示多玩家模式，红蓝双方都是智能体
    # window_size_x=800, window_size_y=600 用于显示游戏窗口，方便观察
    # 如果你希望关闭渲染，可以设置为 0
    env = StarCraft2Env(map_name="3s5z", mode='multi', window_size_x=800, window_size_y=600)

    n_episodes = 5 # 测试的回合数

    print(f"Starting {n_episodes} test episodes on map {env.map_name}...")

    # 初始化红蓝双方的策略
    # PolicyRed 和 PolicyBlue 会在 __init__ 中加载各自的模型
    red_policy = PolicyRed()
    blue_policy = PolicyBlue()

    total_red_wins = 0
    total_blue_wins = 0
    total_draws = 0

    for e in range(n_episodes):
        env.reset() # 每个回合开始时重置环境
        terminated = False
        red_episode_reward = 0
        blue_episode_reward = 0
        episode_steps = 0

        print(f"\n--- Episode {e+1}/{n_episodes} ---")

        while not terminated:
            # 获取红方智能体的动作
            # 注意：red_policy.policy(env) 内部只能使用 env.get_obs() 或 env.get_obs_agent()
            red_actions = red_policy.policy(env)

            # 获取蓝方智能体的动作
            # 注意：blue_policy.policy(env) 内部只能使用 env.get_obs() 或 env.get_obs_agent()
            blue_actions = blue_policy.policy(env)

            # 环境步进，传入红蓝双方的动作列表
            rewards, terminateds, info = env.step([red_actions, blue_actions])
            
            red_episode_reward += rewards[0]
            blue_episode_reward += rewards[1]
            terminated = terminateds[0] # 只要一方终止，整个回合就终止
            episode_steps += 1

            # 可以在这里打印每一步的奖励或状态，用于调试
            # print(f"Step {episode_steps}: Red Reward={rewards[0]}, Blue Reward={rewards[1]}")

        # 回合结束，打印结果
        print(f"Episode {e+1} finished in {episode_steps} steps.")
        print(f"Red Total Reward: {red_episode_reward}")
        print(f"Blue Total Reward: {blue_episode_reward}")

        # 判断胜负
        if info["battle_won"][0]: # 红方获胜
            print("Result: Red Team Wins!")
            total_red_wins += 1
        elif info["battle_won"][1]: # 蓝方获胜
            print("Result: Blue Team Wins!")
            total_blue_wins += 1
        else:
            print("Result: Draw (Episode Limit Reached or Both Died Simultaneously).")
            total_draws += 1

    # 所有回合测试结束，打印总统计
    print("\n--- Test Summary ---")
    print(f"Total Episodes: {n_episodes}")
    print(f"Red Wins: {total_red_wins}")
    print(f"Blue Wins: {total_blue_wins}")
    print(f"Draws: {total_draws}")

    env.close() # 关闭环境

if __name__ == '__main__':
    main()
