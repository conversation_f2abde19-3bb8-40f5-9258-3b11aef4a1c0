import torch as th
import numpy as np
import os
from types import SimpleNamespace as SN

# 从提交仓库的相对路径导入必要的模块
# 确保这些模块文件已复制到 submission/team1/ 目录下对应的子文件夹中
from .modules.agents import REGISTRY as agent_REGISTRY # 相对导入
from .modules.mixers import REGISTR<PERSON> as mixer_REGISTRY # 相对导入

class PolicyBlue:
    def __init__(self):
        # 定义模型参数，这些参数必须与训练时使用的参数一致
        # 它们通常来自 qmix.yaml 和 default.yaml
        self.args = SN()
        self.args.n_agents = 8  # 3s5z 地图的智能体数量
        self.args.n_actions = 14 # 3s5z 地图的动作数量 (6个基本动作 + 8个攻击动作)
        self.args.rnn_hidden_dim = 64
        self.args.mixing_embed_dim = 32
        self.args.hypernet_embed = 64
        self.args.obs_agent_id = True
        self.args.obs_last_action = True
        self.args.use_cuda = th.cuda.is_available() # 根据实际设备设置
        self.args.device = "cuda" if self.args.use_cuda else "cpu"
        self.args.use_layer_norm = False # 假设��练时没有使用 LayerNorm
        self.args.use_orthogonal = False # 假设训练时没有使用正交初始化
        self.args.gain = 0.01 # 正交初始化增益，如果 use_orthogonal 为 True 则需要
        self.args.abs = True # QMixer 的 abs 参数
        self.args.state_shape = 216 # 根据模型文件确定的实际状态形状
                                    # 仅用于 QMixer 初始化，不影响 Agent 输入

        # 计算智能体网络的输入形状
        # obs_shape (80) + n_actions (14) + n_agents (8) = 102
        # 但根据模型文件，实际输入形状应该是 150
        agent_input_shape = 150

        # 初始化智能体网络 (n_rnn)
        self.agent = agent_REGISTRY["n_rnn"](agent_input_shape, self.args)

        # 初始化混合网络 (qmix)
        self.mixer = mixer_REGISTRY["qmix"](self.args)

        # 加载模型权重
        current_dir = os.path.dirname(__file__)
        model_path = os.path.join(current_dir, "models")

        try:
            self.agent.load_state_dict(th.load(os.path.join(model_path, "agent.th"), map_location=self.args.device))
            self.mixer.load_state_dict(th.load(os.path.join(model_path, "mixer.th"), map_location=self.args.device))
            print(f"PolicyBlue: Models loaded successfully from {model_path}")
        except FileNotFoundError:
            print(f"PolicyBlue: Model files not found in {model_path}. Ensure agent.th and mixer.th are present.")
            pass 

        # 设置模型为评估模式
        self.agent.eval()
        self.mixer.eval()

        # 将模型移动到正确的设备
        if self.args.use_cuda:
            self.agent.to(self.args.device)
            self.mixer.to(self.args.device)

        # 用于 RNN 的隐藏状态
        self.hidden_state = None
        self.last_action_onehot = None # 用于 obs_last_action

    def policy(self, env):
        # 在每个回合开始时初始化隐藏状态和上次动作
        if env._episode_steps == 0:
            self.hidden_state = self.agent.init_hidden().unsqueeze(0).expand(1, self.args.n_agents, -1) # (1, n_agents, rnn_hidden_dim)
            self.last_action_onehot = th.zeros(1, self.args.n_agents, self.args.n_actions, device=self.args.device) # (1, n_agents, n_actions)

        obs_all_agents_np = env.get_obs()
        avail_actions_all_agents_np = env.get_avail_actions()

        # 将 numpy 数组转换为 PyTorch 张量
        obs_tensor = th.tensor(np.array(obs_all_agents_np), dtype=th.float32, device=self.args.device).unsqueeze(0) # (1, n_agents, obs_shape)
        avail_actions_tensor = th.tensor(np.array(avail_actions_all_agents_np), dtype=th.float32, device=self.args.device).unsqueeze(0) # (1, n_agents, n_actions)

        # 构建智能体输入 (与 BasicMAC._build_inputs 逻辑类似)
        inputs = [obs_tensor]
        if self.args.obs_last_action:
            inputs.append(self.last_action_onehot)
        if self.args.obs_agent_id:
            agent_ids = th.eye(self.args.n_agents, device=self.args.device).unsqueeze(0).expand(1, -1, -1)
            inputs.append(agent_ids)
        
        # 将所有输入拼接起来
        agent_inputs = th.cat([x.reshape(1, self.args.n_agents, -1) for x in inputs], dim=-1)

        # 通过智能体网络获取 Q 值和更新隐藏状态
        agent_outs, self.hidden_state = self.agent(agent_inputs, self.hidden_state) # (1, n_agents, n_actions)

        # 掩码不可用动作
        masked_q_values = agent_outs.clone()
        masked_q_values[avail_actions_tensor == 0] = -float("inf") # 设为负无穷，确保不可用动作不被选中

        # 选择贪婪动作 (在评估模式下通常是贪婪策略)
        chosen_actions_tensor = masked_q_values.max(dim=2)[1] # (1, n_agents)

        # 调试：检查选择的动作是否可用
        for agent_id in range(self.args.n_agents):
            action = chosen_actions_tensor[0, agent_id].item()
            if avail_actions_tensor[0, agent_id, action].item() == 0:
                print(f"Warning: Blue Agent {agent_id} selected unavailable action {action}")
                print(f"Available actions: {avail_actions_tensor[0, agent_id]}")
                print(f"Q-values: {agent_outs[0, agent_id]}")
                print(f"Masked Q-values: {masked_q_values[0, agent_id]}")
                # 强制选择第一个可用动作
                available_actions = th.where(avail_actions_tensor[0, agent_id] == 1)[0]
                if len(available_actions) > 0:
                    chosen_actions_tensor[0, agent_id] = available_actions[0]

        # 更新 last_action_onehot
        self.last_action_onehot = th.zeros(1, self.args.n_agents, self.args.n_actions, device=self.args.device)
        self.last_action_onehot.scatter_(2, chosen_actions_tensor.unsqueeze(-1), 1)

        # 将 PyTorch 张量转换为 Python 列表或 NumPy 数组返回
        chosen_actions_list = chosen_actions_tensor.squeeze(0).tolist() # (n_agents,)

        return chosen_actions_list
