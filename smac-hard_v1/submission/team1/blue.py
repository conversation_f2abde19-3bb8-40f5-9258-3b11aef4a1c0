import torch as th
import numpy as np
import os
from types import SimpleNamespace as SN

# 从提交仓库的相对路径导入必要的模块
from .modules.agents import REGISTRY as agent_REGISTRY
from .modules.mixers import REGISTRY as mixer_REGISTRY

class PolicyBlue:
    def __init__(self):
        self.args = SN()
        self.args.n_agents = 8
        self.args.n_actions = 14
        self.args.rnn_hidden_dim = 64
        self.args.mixing_embed_dim = 32
        self.args.hypernet_embed = 64
        self.args.obs_agent_id = True
        self.args.obs_last_action = True
        self.args.use_cuda = th.cuda.is_available()
        self.args.device = "cuda" if self.args.use_cuda else "cpu"
        self.args.use_layer_norm = False
        self.args.use_orthogonal = False
        self.args.gain = 0.01
        self.args.abs = True
        self.args.state_shape = 200

        agent_input_shape = 226

        self.agent = agent_REGISTRY["n_rnn"](agent_input_shape, self.args)
        self.mixer = mixer_REGISTRY["qmix"](self.args)

        current_dir = os.path.dirname(__file__)
        model_path = os.path.join(current_dir, "models")

        try:
            self.agent.load_state_dict(th.load(os.path.join(model_path, "agent.th"), map_location=self.args.device))
            self.mixer.load_state_dict(th.load(os.path.join(model_path, "mixer.th"), map_location=self.args.device))
            print(f"PolicyBlue: Models loaded successfully from {model_path}")
        except FileNotFoundError:
            print(f"PolicyBlue: Model files not found in {model_path}. Ensure agent.th and mixer.th are present.")
            pass 

        self.agent.eval()
        self.mixer.eval()

        if self.args.use_cuda:
            self.agent.to(self.args.device)
            self.mixer.to(self.args.device)

        self.hidden_state = None
        self.last_action_onehot = None

    def policy(self, env):
        if env._episode_steps == 0:
            self.hidden_state = self.agent.init_hidden().unsqueeze(0).expand(1, self.args.n_agents, -1)
            self.last_action_onehot = th.zeros(1, self.args.n_agents, self.args.n_actions, device=self.args.device)

        obs_all_agents_np = env.get_obs()
        avail_actions_all_agents_np = env.get_avail_actions()

        obs_tensor = th.tensor(obs_all_agents_np, dtype=th.float32, device=self.args.device).unsqueeze(0)
        avail_actions_tensor = th.tensor(avail_actions_all_agents_np, dtype=th.int32, device=self.args.device).unsqueeze(0)

        inputs = [obs_tensor]
        if self.args.obs_last_action:
            inputs.append(self.last_action_onehot)
        if self.args.obs_agent_id:
            agent_ids = th.eye(self.args.n_agents, device=self.args.device).unsqueeze(0).expand(1, -1, -1)
            inputs.append(agent_ids)
        
        agent_inputs = th.cat([x.reshape(1, self.args.n_agents, -1) for x in inputs], dim=-1)

        agent_outs, self.hidden_state = self.agent(agent_inputs, self.hidden_state)

        masked_q_values = agent_outs.clone()
        masked_q_values[avail_actions_tensor == 0] = -float("inf")

        chosen_actions_tensor = masked_q_values.max(dim=2)[1]

        # --- 修正不可用动作的逻辑 --- START
        chosen_actions_list = chosen_actions_tensor.squeeze(0).tolist()
        final_actions = []
        for agent_id, action_id in enumerate(chosen_actions_list):
            current_avail_actions = avail_actions_all_agents_np[agent_id]
            
            if current_avail_actions[action_id] == 0:
                available_indices = np.nonzero(current_avail_actions)[0]
                if len(available_indices) > 0:
                    if 0 in available_indices:
                        final_actions.append(0)
                    else:
                        final_actions.append(available_indices[0])
                else:
                    final_actions.append(0)
            else:
                final_actions.append(action_id)
        # --- 修正不可用动作的逻辑 --- END

        self.last_action_onehot = th.zeros(1, self.args.n_agents, self.args.n_actions, device=self.args.device)
        self.last_action_onehot.scatter_(2, th.tensor(final_actions, dtype=th.long, device=self.args.device).unsqueeze(0).unsqueeze(-1), 1)

        return final_actions