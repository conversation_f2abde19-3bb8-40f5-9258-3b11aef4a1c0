import numpy as np

class PolicyBlue():

    def __init__(self):
        pass

    def policy(self, env):
        env_info = env.get_env_info()
        n_actions = env_info[1]["n_actions"]
        n_agents = env_info[1]["n_agents"]

        obs = env.get_obs(1)    
        state = env.get_state(1)

        actions = []

        for agent_id in range(n_agents):
            avail_actions = env.get_avail_agent_actions(agent_id, player_id=1)
            avail_actions_ind = np.nonzero(avail_actions)[0]
            action = np.random.choice(avail_actions_ind)
            actions.append(action)

        return actions
    