import numpy as np

class PolicyRed():

    def __init__(self):
        pass

    def policy(self, env):
        env_info = env.get_env_info()
        n_actions = env_info[0]["n_actions"]
        n_agents = env_info[0]["n_agents"]

        obs = env.get_obs(0)    
        state = env.get_state(0)

        actions = []

        for agent_id in range(n_agents):
            avail_actions = env.get_avail_agent_actions(agent_id, player_id=0)
            avail_actions_ind = np.nonzero(avail_actions)[0]
            action = np.random.choice(avail_actions_ind)
            actions.append(action)

        return actions
    